package com.ryinex.accountant.client.shared.domain.projects.expenses

import cafe.adriel.voyager.core.model.ScreenModel
import cafe.adriel.voyager.core.model.screenModelScope
import com.ryinex.accountant.client.shared.data.beneficiaries.repositories.BeneficiariesRepository
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.data.projects.repositories.ProjectsRepository
import com.ryinex.accountant.client.shared.domain.projects.expenses.models.ScreenState
import com.ryinex.accountant.client.shared.domain.projects.expenses.repositories.ScreenStateRepository
import com.ryinex.accountant.client.shared.data.terms.repositories.TermsGroupsRepository
import com.ryinex.accountant.client.shared.data.terms.repositories.TermsRepository
import com.ryinex.accountant.client.shared.data.users.repositories.UsersRepository
import com.ryinex.accountant.client.shared.domain.welcome.login.handle
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.DeferHolder
import com.ryinex.accountant.client.shared.data.projects.models.ExpensesFormResponse
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.domain.common.utils.csv
import com.ryinex.accountant.client.shared.domain.welcome.login.deferred
import com.ryinex.kotlin.csv.CsvReadWrite
import kotlinx.coroutines.delay
import kotlinx.coroutines.plus
import kotlin.coroutines.CoroutineContext

class ViewModel(
    private val status: StatusRepository,
    private val users: UsersRepository,
    private val projects: ProjectsRepository,
    private val termsGroups: TermsGroupsRepository,
    private val terms: TermsRepository,
    private val beneficiaries: BeneficiariesRepository,
    private val coroutinesContext: CoroutineContext
) : ScreenModel {
    private val scope = screenModelScope + coroutinesContext

    val screenState = ScreenStateRepository(status)

    fun updateSearch(search: String) {
        screenState.update { it.copy(search = search) }
    }

    fun initScreen(project: Project) = scope.handle {
        screenState.update { it.copy(project = project) }
        delay(100)
        refreshScreen()
    }

    fun refreshScreen() = scope.handle {
        val result = status.execute(
            loading = Message.fromString("جاري تحميل المشروع ..."),
            success = Message.fromString("تم تحميل المشروع"),
            job = {
                val defers = DeferHolder(this)

                val project = defers.add { screenState.get().getProjectOrThrow() }
                val expenses = defers.add { projects.getProjectExpensesByProjectId(project.await().id) }
                val updatedProject = defers.add { projects.getProjectById(project.await().id) }
                val termsGroups = defers.add { termsGroups.getGroupsOfOrganization() }
                val terms = defers.add { terms.getTermsOfOrganization() }
                val beneficiaries = defers.add { beneficiaries.getBeneficiariesOfOrganization() }

                defers.awaitAll()

                screenState.update {
                    ScreenState(
                        project = updatedProject.value,
                        expenses = expenses.value,
                        termsGroups = termsGroups.value,
                        terms = terms.value,
                        beneficiaries = beneficiaries.value,
                        status = it.status,
                        loggedInUser = users.getLoggedInUser(),
                        search = ""
                    )
                }
            }
        )

        if (result is AsyncResult.Fail) throw result.error
    }

    fun addExpense(
        beneficiaryId: Long?,
        termId: Long?,
        termsGroupId: Long?,
        amount: AppDouble,
        description: String,
        transactionDateIseoString: String
    ) = scope.handle {
        val result = status.execute(
            loading = Message.fromString("جاري إضافة مصاريف ..."),
            success = Message.fromString("تم إضافة مصاريف"),
            job = {
                val project = screenState.get().getProjectOrThrow()
                val expense = projects.addExpense(
                    projectId = project.id,
                    beneficiaryId = beneficiaryId,
                    termId = termId,
                    amount = amount,
                    description = description,
                    transactionDateIseoString = transactionDateIseoString,
                    termsGroupId = termsGroupId
                )
                refreshScreen()
            }
        )

        if (result is AsyncResult.Fail) throw result.error
    }

    fun addBeneficiary(name: String, phoneNumber: String, secondaryPhoneNumber: String) = scope.handle {
        val result = status.execute(
            loading = Message.fromString("جاري إضافة مستفيد ..."),
            success = Message.fromString("تم إضافة مستفيد"),
            job = {
                val beneficiary = beneficiaries.createBeneficiary(name, phoneNumber, secondaryPhoneNumber)
                val beneficiaries = beneficiaries.getBeneficiariesOfOrganization()
                screenState.update { it.copy(beneficiaries = beneficiaries) }
            }
        )

        if (result is AsyncResult.Fail) throw result.error
    }

    fun extractExpenses(expenses: List<ProjectExpense>) = scope.handle {
        val file = expenses.csv(screenState.get().getProjectOrThrow().name)

        CsvReadWrite.save(file)
    }

    suspend fun expensesForm(bytes: ByteArray): ExpensesFormResponse? = scope.deferred {
        val prompt = """
            key: transactionAmount -> description: إجمالي المبلغ المدفوع كأرقام فقط بدون أي وحدات  على هيئة string
            key: beneficiary -> description: the entity whom will receive the money
            key: project -> description: for what project or job this transaction is being made
            key: notes -> description: any notes or description mentioned in input
            key: term -> description: which specific summarized term (بند) of the project terms that this money is spent in
        """.trimIndent()
        return@deferred projects.expensesForm(prompt, bytes)
    }.await()
}