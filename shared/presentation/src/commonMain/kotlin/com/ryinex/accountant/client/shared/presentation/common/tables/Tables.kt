package com.ryinex.accountant.client.shared.presentation.common.tables

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.numberFormat
import com.ryinex.accountant.client.shared.data.customers.models.CustomerTransaction
import com.ryinex.accountant.client.shared.data.organization.models.OrganizationCapitalTransaction
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.data.projects.models.ProjectIncome
import com.ryinex.accountant.client.shared.data.users.models.BalanceTransaction
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.kotlin.datatable.data.DataTable
import com.ryinex.kotlin.datatable.data.DataTableColumnLayout
import com.ryinex.kotlin.datatable.data.DataTableConfig
import com.ryinex.kotlin.datatable.data.composable
import com.ryinex.kotlin.datatable.data.setList
import com.ryinex.kotlin.datatable.data.text
import core.common.extensions.dateFormat

@Composable
internal fun projectExpenseTable(
    lazyState: LazyListState,
    items: List<ProjectExpense>,
    isEditEnabled: Boolean,
    onEdit: (ProjectExpense) -> Unit
): DataTable<ProjectExpense> {
    val scope = rememberCoroutineScope()
    val config = defaultConfig()
    var enableEdit by remember { mutableStateOf(isEditEnabled) }

    val table = remember {
        val table = DataTable<ProjectExpense>(
            config = config.copy(column = config.column.copy(layout = DataTableColumnLayout.ScrollableKeepLargest)),
            scope = scope,
            lazyState = lazyState,
        )
            .composable(name = " ") { index, item ->
                if (enableEdit) {
                    AppSupportImageViewButton(
                        painter = rememberVectorPainter(Icons.Default.Edit),
                        enabled = true,
                        onClick = { onEdit(item) }
                    )
                }
            }
            .number(name = "القيمة", value = { data -> data.beneficiaryTransaction.amount })
            .number(name = "تم تحصيل", value = { data -> data.beneficiaryTransaction.amountPaid })
            .text("المشروع") { _, data -> data.project.name }
            .text("الوصف") { _, data -> data.beneficiaryTransaction.description }
            .text("المستفيد") { _, data -> data.beneficiary?.name ?: "" }
            .text("البند") { _, data -> data.term?.name ?: "" }
            .text("مجموعة الأعمال") { _, data -> data.termsGroup?.name ?: "" }
            .text("بواسطة") { _, data -> data.beneficiaryTransaction.createdByDetails.name }
            .date { data -> data.beneficiaryTransaction.createdAt }

            .setList(emptyList(), key = { index, it -> it.id })

        table.enableInteractions(true)
        table
    }

    LaunchedEffect(items) { table.setList(items, key = { index, it -> it.id }) }
    LaunchedEffect(isEditEnabled) { enableEdit = isEditEnabled }

    return table
}

@Composable
internal fun userTransactionsTable(
    lazyState: LazyListState,
    items: List<BalanceTransaction>
): DataTable<BalanceTransaction> {
    val scope = rememberCoroutineScope()
    val config = defaultConfig()
    val table = remember {
        DataTable<BalanceTransaction>(
            config = config,
            lazyState = lazyState,
            scope = scope
        )
            .number(name = "القيمة", value = { data -> data.amount })
            .text("الوصف") { _, it -> it.description }
            .text("بواسطة") { _, it -> it.createdBy.name }
            .date { it.createdAt }
    }

    LaunchedEffect(Unit) { table.enableInteractions(true) }
    LaunchedEffect(items) { table.setList(items) }

    return table
}

@Composable
internal fun capitalTransactionsTable(
    lazyState: LazyListState,
    items: List<OrganizationCapitalTransaction>,
): DataTable<OrganizationCapitalTransaction> {
    val scope = rememberCoroutineScope()
    val config = defaultConfig()
    val table = remember {
        DataTable<OrganizationCapitalTransaction>(
            lazyState = lazyState,
            config = config,
            scope = scope,
        )
            .number("القيمة") { it.amount }
            .text("الوصف") { _, it -> it.description }
            .text("بواسطة") { _, it -> it.createdByDetails.name }
            .date("بتاريخ") { it.createdAt }
    }
    LaunchedEffect(Unit) { table.enableInteractions(true) }
    LaunchedEffect(items) { table.setList(items) }

    return table
}

@Composable
internal fun customersTransactionsTable(
    lazyState: LazyListState,
    items: List<ProjectIncome>,
    isEditEnabled: Boolean,
    isPaymentEnabled: Boolean,
    onEdit: (ProjectIncome) -> Unit,
    onPay: (ProjectIncome) -> Unit
): DataTable<ProjectIncome> {
    val scope = rememberCoroutineScope()
    val config = defaultConfig()
    var editEnabled by remember { mutableStateOf(isEditEnabled) }
    var paymentEnabled by remember { mutableStateOf(isPaymentEnabled) }

    val table = remember {
        DataTable<ProjectIncome>(
            lazyState = lazyState,
            config = config,
            scope = scope,
        )
            .composable("") { i, item ->
                if (editEnabled) {
                    AppSupportImageViewButton(
                        painter = rememberVectorPainter(Icons.Default.Edit),
                        enabled = true,
                        onClick = { onEdit(item) }
                    )
                }
            }
            .number("القيمة") { it.customerTransaction.amount }
            .number("تم تحصيل") { it.customerTransaction.amountPaid }
            .composable("") { i, item ->
                if (paymentEnabled && item.customerTransaction.amountPaid < item.customerTransaction.amount) {
                    AppSupportImageViewButton(
                        painter = rememberVectorPainter(Icons.Default.Edit),
                        enabled = true,
                        onClick = { onPay(item) }
                    )
                }
            }
            .number("المتبقى") { it.customerTransaction.remaining }
            .text("المشروع") { _, it -> it.project.name }
            .text("الوصف") { _, it -> it.customerTransaction.description }
            .text("بواسطة") { _, it -> it.customerTransaction.createdByDetails.name }
            .date("بتاريخ") { it.customerTransaction.createdAt }
    }
    LaunchedEffect(isEditEnabled) { editEnabled = isEditEnabled }
    LaunchedEffect(isPaymentEnabled) { paymentEnabled = isPaymentEnabled }
    LaunchedEffect(Unit) { table.enableInteractions(true) }
    LaunchedEffect(items) { table.setList(items) }

    return table
}

@Composable
private fun defaultConfig(): DataTableConfig {
    val contentColor = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.3f)

    return DataTableConfig.default(
        verticalSpacing = 0,
        horizontalSpacing = 0,
        column = {
            it.copy(
                cell = it.cell.copy(
                    modifier = it.cell.modifier.border(Dp.Hairline, contentColor),
                    padding = PaddingValues(horizontal = 4.dp)
                )
            )
        }
    )
}

private fun <T : Any> DataTable<T>.date(name: String = "تاريخ", value: (T) -> String): DataTable<T> {
    return text(
        name = name,
        presentation = { _, _, it -> it.copy(isForceLtr = true) },
        textMapper = { it.dateFormat() },
        value = { _, data -> value(data) }
    )
}

private fun <T : Any> DataTable<T>.number(name: String, value: (T) -> AppDouble): DataTable<T> {
    return text(
        name = name,
        presentation = { _, _, it -> it.copy(isForceLtr = true) },
        textMapper = { it.formatted() },
        value = { _, data -> value(data) }
    )
}
