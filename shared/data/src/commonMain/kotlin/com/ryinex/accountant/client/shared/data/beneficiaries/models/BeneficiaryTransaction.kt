package com.ryinex.accountant.client.shared.data.beneficiaries.models

import com.ryinex.accountant.client.shared.data.users.models.User
import com.ryinex.accountant.client.shared.data.common.utilities.AppDoubleJson
import com.ryinex.accountant.client.shared.data.common.utilities.JavaSerializable
import kotlinx.serialization.Serializable

@Serializable
data class BeneficiaryTransaction(
    val id: Long,
    val organizationId: Long,
    val beneficiaryId: Long?,

    val amount: AppDouble<PERSON>son,
    val amountPaid: AppDoubleJson,
    val description: String,
    val transactionDate: String,

    val createdAt: String,
    val createdBy: Long,
    val updatedAt: String,
    val updatedBy: Long,
    val version: Long,

    val createdByDetails: User
): JavaSerializable