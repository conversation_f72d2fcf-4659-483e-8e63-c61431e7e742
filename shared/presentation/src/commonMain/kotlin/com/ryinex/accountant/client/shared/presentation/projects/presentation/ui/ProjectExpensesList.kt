package com.ryinex.accountant.client.shared.presentation.projects.presentation.ui

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.resources.Res
import com.ryinex.accountant.client.resources.ic_mic
import com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui.AddBeneficiarySheet
import com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui.BeneficiaryTransactionsListSection
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.data.beneficiaries.models.Beneficiary
import com.ryinex.accountant.client.shared.domain.projects.expenses.ViewModel
import com.ryinex.accountant.client.shared.domain.projects.expenses.models.ScreenState
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.data.common.utilities.app
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.presentation.common.DateTimePicker
import com.ryinex.accountant.client.shared.presentation.common.DesignSystem
import com.ryinex.accountant.client.shared.presentation.common.checkbox.AppCheckbox
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.dialogs.VoiceDialog
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.common.texts.AppBodyText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledBodyTextHorizontal
import com.ryinex.accountant.client.shared.presentation.terms.AddTermSheet
import com.ryinex.kotlin.datatable.data.DataTable
import core.common.extensions.format
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import org.jetbrains.compose.resources.painterResource

fun LazyListScope.ProjectExpensesList(
    table: DataTable<ProjectExpense>,
    scrollState: ScrollState,
    state: ScreenState,
    viewModel: ViewModel,
    isDesktop: Boolean
) {
    item {
        AppTextField(
            modifier = Modifier.fillMaxWidth(),
            text = state.search,
            hint = "بحث",
            enabled = true,
            isError = false,
            onValueChange = viewModel::updateSearch,
            errorText = ""
        )
    }


    BeneficiaryTransactionsListSection(
        table = table,
        transactions = state.filteredExpense,
        terms = state.terms,
        termsGroups = state.termsGroups,
        isPaymentEnabled = false,
        isEditEnabled = state.isExpensesEditEnabled,
        isDesktop = isDesktop,
        onRefresh = { viewModel.refreshScreen() },
        scrollState = scrollState
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AddExpensesSheet(
    state: ScreenState,
    viewModel: ViewModel,
    onDismissRequest: () -> Unit
) {
    val minDescriptionLength = remember { 3 }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var amount by remember { mutableStateOf(0.0.app()) }
        var description by remember { mutableStateOf("") }
        var beneficiaryText by remember { mutableStateOf("") }
        var beneficiary by remember { mutableStateOf<Beneficiary?>(null) }
        var generalBeneficiary by remember { mutableStateOf(false) }
        val termsGroup = remember { mutableStateOf<TermsGroup?>(null) }
        val generalTerm = remember { mutableStateOf(false) }
        val termText = remember { mutableStateOf("") }
        val term = remember { mutableStateOf<Term?>(null) }
        var currentDate by remember {
            mutableStateOf(Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()))
        }

        val isAmountError by remember(amount) { mutableStateOf(amount == 0.0.app()) }
        val isDescriptionError by remember(description) { mutableStateOf(description.trim().length < minDescriptionLength) }

        val isEnabled by remember(
            isAmountError,
            isDescriptionError,
            beneficiary,
            generalBeneficiary,
            term.value,
            termsGroup.value,
            generalTerm.value
        ) {
            val validTerms = (termsGroup.value != null && term.value != null) || generalTerm.value
            val validBeneficiary = beneficiary != null || generalBeneficiary
            mutableStateOf(!isAmountError && !isDescriptionError && validBeneficiary && validTerms)
        }

        AppLazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            item {
                AppSectionTitleText("إضافة مصاريف")
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    val scope = rememberCoroutineScope()
                    VoiceRecordButtons {
                        scope.launch {
                            val response = viewModel.expensesForm(it) ?: return@launch
                            if (response.transactionAmount != null && response.transactionAmount!!.toDoubleOrNull() != null) {
                                amount = response.transactionAmount!!.toDouble().app()
                            }
                            if (response.notes != null) description = response.notes!!
                            if (response.beneficiary != null) beneficiaryText = response.beneficiary!!
                            if (response.term != null) termText.value = response.term!!
                        }
                    }

                    AppTextButton(
                        modifier = Modifier,
                        text = "إلغاء",
                        enabled = true,
                        onClick = { onDismissRequest() }
                    )

                    AppTextButton(
                        modifier = Modifier,
                        text = "إضافة",
                        enabled = isEnabled,
                        onClick = {
                            viewModel.addExpense(
                                beneficiaryId = beneficiary?.id,
                                termId = term.value?.id,
                                amount = amount,
                                description = description,
                                transactionDateIseoString = currentDate.toInstant(TimeZone.currentSystemDefault())
                                    .toString(),
                                termsGroupId = termsGroup.value?.id
                            )
                            onDismissRequest()
                        },
                    )
                }
            }

            item {
                AppDecimalTextField(
                    modifier = Modifier.fillMaxWidth(),
                    amount = amount,
                    hint = "القيمة",
                    enabled = true,
                    isError = isAmountError,
                    onlyPositive = true,
                    onValueChange = { amount = it },
                )
            }

            item {
                AppTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = description,
                    hint = "الوصف",
                    enabled = true,
                    isError = isDescriptionError,
                    onValueChange = { description = it },
                    errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف"
                )
            }

            item(termsGroup.value) {
                val showAddSheet = remember { mutableStateOf(false) }
                TermsInput(termText, state, termsGroup, term, generalTerm, showAddSheet)

                if (showAddSheet.value) {
                    AddTermSheet(
                        termsGroup = termsGroup.value,
                        groups = state.termsGroups,
                        isCancelTermsGroupEnabled = termsGroup.value == null,
                        onDismissRequest = { showAddSheet.value = false },
                        onFinish = { viewModel.refreshScreen() }
                    )
                }
            }

            item {
                BeneficiaryInput(
                    state = state,
                    beneficiaryText = beneficiaryText,
                    onBeneficiaryTextChange = { beneficiaryText = it },
                    beneficiary = beneficiary,
                    onBeneficiaryChange = { beneficiary = it },
                    viewModel = viewModel
                )
            }

            item {
                DateColumn(currentDate, onDateChange = { currentDate = it })
            }
        }
    }
}

@Composable
private fun TermsInput(
    termText: MutableState<String>,
    state: ScreenState,
    termsGroup: MutableState<TermsGroup?>,
    term: MutableState<Term?>,
    generalTerm: MutableState<Boolean>,
    showAddSheet: MutableState<Boolean>
) {
    AppRow(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
        alignment = Alignment.CenterVertically
    ) {
        if (!generalTerm.value) {
            Box(modifier = Modifier.weight(1f)) {
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = termText.value,
                    hint = "البند",
                    enabled = true,
                    isError = false,
                    minSuggestLength = 0,
                    errorText = "",
                    allItems = state.terms.filter {
                        if (termsGroup.value == null) true else it.termsGroupId == termsGroup.value!!.id
                    },
                    searchMapper = { "${it.name} - ${it.termsGroup.name}" },
                    displayMapper = { "${it.name} - ${it.termsGroup.name}" },
                    onItemClick = {
                        term.value = it
                        termsGroup.value = state.termsGroups.find { group -> it.termsGroupId == group.id }
                    },
                    onEmptyItemClick = {
                        term.value = null
                        showAddSheet.value = true
                    },
                    items = state.terms.filter {
                        if (termsGroup.value == null) {
                            true
                        } else it.termsGroupId == termsGroup.value!!.id
                    },
                    onValueChange = { termText.value = it },
                    selected = term.value,
                    onCancelSelected = {
                        term.value = null
                        termsGroup.value = null
                    }
                )
            }
        } else {
            AppBodyText("البند")
        }

        if (termsGroup.value == null) {
            AppCheckbox(label = "عام", checked = generalTerm.value, onCheckedChange = { generalTerm.value = it })
        }
    }

}

@Composable
private fun DateColumn(currentDate: LocalDateTime, onDateChange: (LocalDateTime) -> Unit) {
    AppColumn(
        modifier = Modifier.fillMaxWidth(),
        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.Top),
        alignment = Alignment.Start
    ) {
        DateTimePicker(
            modifier = Modifier.fillMaxWidth(),
            now = currentDate,
            onPicked = onDateChange
        )

        AppLabeledBodyTextHorizontal(label = "التاريخ", text = currentDate.format())
    }
}

@Composable
private fun BeneficiaryInput(
    state: ScreenState,
    beneficiaryText: String,
    onBeneficiaryTextChange: (String) -> Unit,
    beneficiary: Beneficiary?,
    onBeneficiaryChange: (Beneficiary?) -> Unit,
    viewModel: ViewModel
) {
    var showAddSheet by remember { mutableStateOf(false) }
    AppSelectableAutoCompleteTextField(
        modifier = Modifier.fillMaxWidth(),
        text = beneficiaryText,
        hint = "المستفيد",
        enabled = true,
        isError = false,
        minSuggestLength = 0,
        errorText = "",
        allItems = state.beneficiaries,
        searchMapper = { it.name },
        displayMapper = { it.name },
        onItemClick = { onBeneficiaryChange(it) },
        onEmptyItemClick = if (state.loggedInUser?.isAdmin == true) {
            {
                onBeneficiaryTextChange("")
                showAddSheet = true
            }
        } else null,
        items = state.beneficiaries,
        onValueChange = onBeneficiaryTextChange,
        selected = beneficiary,
@Composable
fun VoiceRecordButtons(
    onRecord: (ByteArray) -> Unit
) {
    var showDialog by remember { mutableStateOf(false) }

    AppSupportImageViewButton(
        enabled = true,
        painter = painterResource(Res.drawable.ic_mic),
        onClick = { showDialog = true },
    )

    if (showDialog) {
        VoiceDialog(
            bestResults = "لأفضل نتيجة تحدث بهذه الصيغة:\nتم دفع قيمة .... المستفيد .... تحت بند .... الوصف ....",
            onDismiss = { showDialog = false },
            onFinish = onRecord
        )
    }
}