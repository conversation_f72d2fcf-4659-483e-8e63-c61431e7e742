package com.ryinex.accountant.client.shared.presentation.beneficiaries.presentation.ui

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.ModalBottomSheetDefaults
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.rememberVectorPainter
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppButton
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppTextButton
import com.ryinex.accountant.client.shared.presentation.common.containers.AppCard
import com.ryinex.accountant.client.shared.presentation.common.containers.AppColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppLazyColumn
import com.ryinex.accountant.client.shared.presentation.common.containers.AppRow
import com.ryinex.accountant.client.shared.presentation.common.containers.ForceLtr
import com.ryinex.accountant.client.shared.presentation.common.dimensions.Dimensions
import com.ryinex.accountant.client.shared.presentation.common.images.AppSupportImageViewButton
import com.ryinex.accountant.client.shared.presentation.common.texts.AppDecimalTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabelText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppLabeledLabelTextHorizontal
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSectionTitleText
import com.ryinex.accountant.client.shared.presentation.common.texts.AppSelectableAutoCompleteTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTextField
import com.ryinex.accountant.client.shared.presentation.common.texts.AppTitleText
import com.ryinex.accountant.client.shared.data.beneficiaries.repositories.BeneficiariesTransactionsRepository
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.data.projects.repositories.ProjectsRepository
import com.ryinex.accountant.client.shared.data.terms.models.Term
import com.ryinex.accountant.client.shared.data.terms.models.TermsGroup
import com.ryinex.accountant.client.shared.data.common.error.ErrorHandler
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.common.utilities.numberFormat
import com.ryinex.accountant.client.shared.presentation.common.DateTimePicker
import com.ryinex.accountant.client.shared.presentation.common.buttons.AppDelayedConfirmButton
import com.ryinex.accountant.client.shared.presentation.common.shadowBluish
import com.ryinex.accountant.client.shared.presentation.common.surface.AppBottomSheet
import com.ryinex.accountant.client.shared.presentation.terms.AddTermSheet
import com.ryinex.kotlin.datatable.data.DataTable
import com.ryinex.kotlin.datatable.data.DataTableConfig
import com.ryinex.kotlin.datatable.data.composable
import com.ryinex.kotlin.datatable.data.text
import com.ryinex.kotlin.datatable.views.EmbeddedDataTableView
import core.common.extensions.dateFormat
import core.common.extensions.dateTime
import core.common.message.Message
import core.common.result.AsyncResult
import core.common.status.StatusRepository
import core.common.status.execute
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime

internal fun LazyListScope.BeneficiaryTransactionsListSection(
    table: DataTable<ProjectExpense>,
    terms: List<Term>,
    termsGroups: List<TermsGroup>,
    transactions: List<ProjectExpense>,
    isEditEnabled: Boolean,
    isPaymentEnabled: Boolean = false,
    isDesktop: Boolean,
    scrollState: ScrollState,
    onRefresh: () -> Unit
) {
    if (isDesktop) {

        EmbeddedDataTableView(scrollState, table)
    } else {
        transactions.forEach { item ->
            item {
                var showEditSheet by remember { mutableStateOf(false) }
                var expense by remember { mutableStateOf<ProjectExpense?>(null) }
                BeneficiaryTransactionCard(
                    expense = item,
                    terms = terms,
                    termsGroups = termsGroups,
                    isEditEnabled = isEditEnabled,
                    isPaymentEnabled = isPaymentEnabled,
                    onFinishEdit = onRefresh
                )

                if (showEditSheet && expense != null) {
                    EditTransactionSheet(
                        expense = expense!!,
                        terms = terms,
                        termsGroups = termsGroups,
                        onDismissRequest = {
                            expense = null
                            showEditSheet = false
                        },
                        onFinish = onRefresh
                    )
                }
            }
        }
    }
}

@Composable
private fun BeneficiaryTransactionCard(
    expense: ProjectExpense,
    isEditEnabled: Boolean,
    isPaymentEnabled: Boolean,
    terms: List<Term>,
    termsGroups: List<TermsGroup>,
    onFinishEdit: () -> Unit,
    modifier: Modifier = Modifier
) {
    AppCard(
        modifier = modifier.fillMaxWidth().shadowBluish()
    ) {
        var extended by remember { mutableStateOf(false) }


        AppColumn(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { extended = !extended }
                .padding(Dimensions.Paddings.medium.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp),
            alignment = Alignment.Start
        ) {
            AppRow(
                modifier = Modifier.fillMaxWidth(),
                arrangement = Arrangement.SpaceBetween,
                alignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth().weight(1f),
                    contentAlignment = if (LayoutDirection.Rtl == LocalLayoutDirection.current) Alignment.CenterStart else Alignment.CenterEnd
                ) {
                    ForceLtr {
                        AppTitleText(
                            text = expense.beneficiaryTransaction.amount.formatted(),
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                AppColumn(
                    arrangement = Arrangement.Top,
                    alignment = Alignment.End
                ) {
                    var showEditSheet by remember { mutableStateOf(false) }

                    if (isPaymentEnabled && expense.beneficiaryTransaction.amountPaid < expense.beneficiaryTransaction.amount) {
                        AppTextButton(
                            modifier = Modifier,
                            text = "تحصيل",
                            enabled = isPaymentEnabled,
                            onClick = {}
                        )
                    }

                    AppRow(
                        arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                        alignment = Alignment.CenterVertically
                    ) {
                        if (isEditEnabled) {
                            AppSupportImageViewButton(
                                painter = rememberVectorPainter(Icons.Default.Edit),
                                enabled = isEditEnabled,
                                onClick = { showEditSheet = true },
                            )
                        }

                        AppSupportImageViewButton(
                            painter = rememberVectorPainter(if (extended) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown),
                            enabled = true,
                            onClick = { extended = !extended },
                            backgroundColor = Color.Transparent,
                            tint = MaterialTheme.colorScheme.tertiary
                        )
                    }

                    if (showEditSheet) {
                        EditTransactionSheet(
                            expense = expense,
                            terms = terms,
                            termsGroups = termsGroups,
                            onDismissRequest = { showEditSheet = false },
                            onFinish = {
                                showEditSheet = false
                                onFinishEdit()
                            }
                        )
                    }
                }

            }

            AppColumn(
                modifier = Modifier,
                arrangement = Arrangement.Top,
                alignment = Alignment.Start
            ) {
                if (expense.beneficiaryTransaction.description.isNotBlank()) {
                    AppLabelText(
                        text = expense.beneficiaryTransaction.description,
                        color = LocalContentColor.current
                    )
                } else {
                    AppLabeledLabelTextHorizontal(
                        label = "البند",
                        text = expense.term?.name ?: ""
                    )
                }

                if (expense.beneficiaryTransaction.amountPaid < expense.beneficiaryTransaction.amount) {
                    AppLabeledLabelTextHorizontal(
                        label = "تم تحصيل",
                        text = expense.beneficiaryTransaction.amountPaid.formatted()
                    )
                }

                if (extended) {
                    AppLabeledLabelTextHorizontal(
                        label = "المستفيد",
                        text = expense.beneficiary?.name ?: ""
                    )
                    AppLabeledLabelTextHorizontal(
                        label = "المشروع",
                        text = expense.project.name
                    )
                    if (expense.beneficiaryTransaction.description.isNotBlank()) {
                        AppLabeledLabelTextHorizontal(
                            label = "البند",
                            text = expense.term?.name ?: ""
                        )
                    }
                    AppLabeledLabelTextHorizontal(
                        label = "المجموعة",
                        text = expense.termsGroup?.name ?: ""
                    )

                    AppLabeledLabelTextHorizontal(
                        label = "بواسطة",
                        text = expense.beneficiaryTransaction.createdByDetails.name
                    )

                    AppLabeledLabelTextHorizontal(
                        label = "بتاريخ",
                        text = expense.beneficiaryTransaction.transactionDate.dateFormat(),
                        ltrText = true
                    )
                }
            }


        }
    }
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun EditTransactionSheet(
    expense: ProjectExpense,
    terms: List<Term>,
    termsGroups: List<TermsGroup>,
    onDismissRequest: () -> Unit,
    onFinish: () -> Unit
) {
    val minDescriptionLength = remember { 3 }
    val scope = rememberCoroutineScope()
    var isLoading by remember { mutableStateOf(false) }

    AppBottomSheet(onDismissRequest = onDismissRequest) {
        var description by remember { mutableStateOf(expense.beneficiaryTransaction.description) }
        var amount by remember { mutableStateOf(expense.beneficiaryTransaction.amount) }
        var amountPaid by remember { mutableStateOf(expense.beneficiaryTransaction.amountPaid) }
        var now by remember { mutableStateOf(expense.beneficiaryTransaction.transactionDate.dateTime()) }
        var termsGroupText by remember { mutableStateOf("") }
        var termsGroup by remember { mutableStateOf<TermsGroup?>(expense.termsGroup) }
        var termText by remember { mutableStateOf("") }
        var term by remember { mutableStateOf<Term?>(expense.term) }

        val isDescriptionError by remember(description) { mutableStateOf(description.length < minDescriptionLength) }
        val isAmountError by remember(amount) { mutableStateOf(false) }
        val isAmountPaidError by remember(amountPaid, amount) { mutableStateOf(amountPaid > amount) }

        val isChangeAmountEnabled by remember(isAmountError, isAmountPaidError, isLoading) {
            mutableStateOf(!isAmountError && !isLoading && !isAmountPaidError)
        }
        val isDetailsEnabled by remember(isDescriptionError, isLoading) {
            mutableStateOf(!isDescriptionError && !isLoading)
        }
        val isTermsEnabled by remember(term, termsGroup, isLoading) {
            mutableStateOf(!isLoading && term != null && termsGroup != null)
        }

        AppLazyColumn(
            modifier = Modifier.fillMaxHeight().padding(Dimensions.Paddings.screen.dp),
            arrangement = Arrangement.spacedBy(Dimensions.Paddings.medium.dp, Alignment.Top),
            alignment = Alignment.Start
        ) {
            item { AppSectionTitleText(text = "تعديل التحويل") }

            item {
                AppDecimalTextField(
                    modifier = Modifier.fillMaxWidth(),
                    amount = amount,
                    hint = "القيمة",
                    enabled = !isLoading,
                    isError = isAmountError,
                    onlyPositive = true,
                    onValueChange = {
                        amount = it
                        amountPaid = it
                    },
                    errorText = ""
                )
            }

//            item {
//                AppDecimalTextField(
//                    modifier = Modifier.fillMaxWidth(),
//                    amount = amountPaid,
//                    hint = "تم تحصيل",
//                    enabled = !isLoading,
//                    isError = isAmountPaidError,
//                    onlyPositive = true,
//                    onValueChange = { amountPaid = it },
//                    errorText = "المبلغ المدفوع يجب أن يكون أقل من القيمة"
//                )
//            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppTextButton(
                        text = "إلغاء",
                        enabled = !isLoading,
                        onClick = { if (!isLoading) onDismissRequest() })

                    AppTextButton(
                        text = "تعديل",
                        enabled = isChangeAmountEnabled,
                        onClick = {
                            scope.launch {
                                isLoading = true
                                changeExpenseAmount(
                                    amount = amount,
                                    amountPaid = amountPaid,
                                    projectExpenseId = expense.id,
                                    projectId = expense.project.id,
                                    transactionVersion = expense.beneficiaryTransaction.version,
                                    onError = { isLoading = false }
                                )
                                isLoading = false
                                onFinish()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }

            item { HorizontalDivider() }

            item {
                AppTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = description,
                    hint = "الوصف",
                    enabled = !isLoading,
                    isError = isDescriptionError,
                    errorText = "الوصف يجب أن يكون على الأقل $minDescriptionLength أحرف",
                    onValueChange = { description = it }
                )
            }

            item {
                DateTimePicker(modifier = Modifier.fillMaxWidth(), now = now) { now = it }
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppTextButton(
                        modifier = Modifier,
                        text = "إلغاء",
                        enabled = !isLoading,
                        onClick = { onDismissRequest() }
                    )

                    AppTextButton(
                        modifier = Modifier,
                        text = "تعديل",
                        enabled = isDetailsEnabled,
                        onClick = {
                            scope.launch {
                                isLoading = true
                                updateBeneficiaryTransaction(
                                    transactionId = expense.beneficiaryTransaction.id,
                                    description = description,
                                    transactionDate = now.toInstant(TimeZone.currentSystemDefault()).toString(),
                                    version = expense.beneficiaryTransaction.version,
                                    onError = { isLoading = false }
                                )
                                isLoading = false
                                onFinish()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }

            item { HorizontalDivider() }

            item(termsGroup) {
                var showAddSheet by remember { mutableStateOf(false) }
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = termText,
                    hint = "البند",
                    enabled = true,
                    isError = false,
                    minSuggestLength = 0,
                    errorText = "",
                    allItems = terms.filter {
                        if (termsGroup == null) true else it.termsGroupId == termsGroup!!.id
                    },
                    searchMapper = { it.name },
                    displayMapper = { it.name },
                    onItemClick = {
                        term = it
                        termsGroup = termsGroups.find { group -> it.termsGroupId == group.id }
                    },
                    onEmptyItemClick = {
                        term = null
                        showAddSheet = true
                    },
                    items = terms.filter {
                        if (termsGroup == null) {
                            true
                        } else it.termsGroupId == termsGroup!!.id
                    },
                    onValueChange = { termText = it },
                    selected = term,
                    onCancelSelected = {
                        term = null
                        termsGroup = null
                    }
                )

                if (showAddSheet) {
                    AddTermSheet(
                        termsGroup = termsGroup,
                        groups = termsGroups,
                        isCancelTermsGroupEnabled = termsGroup == null,
                        onDismissRequest = { showAddSheet = false },
                        onFinish = onFinish,
                    )
                }
            }

            item {
                AppSelectableAutoCompleteTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = termsGroupText,
                    hint = "مجموعة البنود",
                    enabled = true,
                    isError = false,
                    minSuggestLength = 0,
                    errorText = "",
                    allItems = termsGroups,
                    searchMapper = { it.name },
                    displayMapper = { it.name },
                    onItemClick = { termsGroup = it },
                    onEmptyItemClick = null,
                    items = termsGroups,
                    onValueChange = {
                        termsGroupText = it
                    },
                    selected = termsGroup,
                    onCancelSelected = {
                        termsGroup = null
                        term = null
                    }
                )
            }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppTextButton(
                        modifier = Modifier,
                        text = "إلغاء",
                        enabled = !isLoading,
                        onClick = { onDismissRequest() }
                    )

                    AppTextButton(
                        modifier = Modifier,
                        text = "تعديل",
                        enabled = isTermsEnabled,
                        onClick = {
                            scope.launch {
                                isLoading = true
                                updateProjectExpense(
                                    projectExpenseId = expense.id,
                                    projectId = expense.project.id,
                                    termsGroupId = termsGroup!!.id,
                                    termId = term!!.id,
                                    version = expense.version,
                                    onError = { isLoading = false }
                                )
                                isLoading = false
                                onFinish()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }

            item { HorizontalDivider() }

            item {
                AppRow(
                    modifier = Modifier.fillMaxWidth(),
                    arrangement = Arrangement.spacedBy(Dimensions.Paddings.small.dp, Alignment.End),
                    alignment = Alignment.CenterVertically
                ) {
                    AppDelayedConfirmButton(
                        modifier = Modifier,
                        text = "حذف التحويل",
                        enabled = !isLoading,
                        containerColor = MaterialTheme.colorScheme.error,
                        onClick = {
                            scope.launch {
                                isLoading = true
                                deleteExpense(
                                    projectExpenseId = expense.id,
                                    projectId = expense.project.id,
                                    onError = { isLoading = false }
                                )
                                isLoading = false
                                onFinish()
                                onDismissRequest()
                            }
                        }
                    )
                }
            }
        }
    }
}

private suspend fun changeExpenseAmount(
    amount: AppDouble,
    amountPaid: AppDouble,
    projectExpenseId: Long,
    projectId: Long,
    transactionVersion: Long,
    projects: ProjectsRepository = TempDI.projects,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري تعديل القيمة ..."),
            success = Message.fromString("تم تعديل القيمة"),
            job = {
                projects.changeExpenseAmount(
                    amount = amount,
                    amountPaid = amountPaid,
                    projectExpenseId = projectExpenseId,
                    projectId = projectId,
                    transactionVersion = transactionVersion
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun updateBeneficiaryTransaction(
    transactionId: Long,
    description: String,
    transactionDate: String,
    version: Long,
    transactions: BeneficiariesTransactionsRepository = TempDI.expenses,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري تعديل المصاريف ..."),
            success = Message.fromString("تم تعديل المصاريف"),
            job = {
                transactions.update(
                    transactionId = transactionId,
                    description = description,
                    transactionDate = transactionDate,
                    version = version
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun deleteExpense(
    projectExpenseId: Long,
    projectId: Long,
    projects: ProjectsRepository = TempDI.projects,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري حذف التحويل ..."),
            success = Message.fromString("تم حذف التحويل"),
            job = {
                projects.deleteProjectExpense(
                    projectExpenseId = projectExpenseId,
                    projectId = projectId
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}

private suspend fun updateProjectExpense(
    projectExpenseId: Long,
    projectId: Long,
    termsGroupId: Long,
    termId: Long,
    version: Long,
    projects: ProjectsRepository = TempDI.projects,
    status: StatusRepository = TempDI.status,
    errorHandler: ErrorHandler = TempDI.errorHandler,
    onError: () -> Unit
) {
    errorHandler.execute {
        val result = status.execute(
            loading = Message.fromString("جاري تعديل المصاريف ..."),
            success = Message.fromString("تم تعديل المصاريف"),
            job = {
                projects.patchProjectExpense(
                    projectExpenseId = projectExpenseId,
                    projectId = projectId,
                    termsGroupId = termsGroupId,
                    termId = termId,
                    version = version
                )
            }
        )

        if (result is AsyncResult.Fail) {
            onError()
            throw result.error
        }
    }
}