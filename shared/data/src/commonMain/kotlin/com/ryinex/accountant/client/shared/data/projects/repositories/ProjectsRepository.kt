package com.ryinex.accountant.client.shared.data.projects.repositories

import com.ryinex.accountant.client.shared.data.projects.models.Project
import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.accountant.client.shared.data.projects.models.ProjectIncome
import com.ryinex.accountant.client.shared.data.common.utilities.AppDouble
import com.ryinex.accountant.client.shared.data.common.utilities.TempDI
import com.ryinex.accountant.client.shared.data.common.utilities.UserSettings
import com.ryinex.accountant.client.shared.data.projects.models.ExpensesFormResponse
import core.http.client.FormElement
import core.http.client.HttpClient
import core.http.client.HttpFormRequest
import core.http.client.HttpHeader
import core.http.client.HttpRequest
import core.monitoring.common.repository.Logger
import core.monitoring.common.repository.async
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

class ProjectsRepository(
    private val httpClient: HttpClient,
    private val apiBaseUrl: String,
    private val logger: Logger
) {

    suspend fun create(
        name: String,
        description: String,
        isActive: Boolean,
        customerId: Long,
    ): Project = logger.async {
        val url = "$apiBaseUrl/api/v1/projects"
        val body = buildJsonObject {
            put("name", name.trim())
            put("description", description.trim())
            put("customerId", customerId)
            put("isActive", isActive)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(Project.serializer())
        return@async response
    }

    suspend fun getProjectsOfOrganization(): List<Project> = logger.async {
        val url = "$apiBaseUrl/api/v1/projects"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(ListSerializer(Project.serializer()))
        return@async response.sortedBy { it.name }
    }

    suspend fun getProjectById(projectId: Long): Project = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/${projectId}"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(Project.serializer())
        return@async response
    }

    suspend fun getExpensesByBeneficiaryId(beneficiaryId: Long): List<ProjectExpense> = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/expenses/beneficiaries/$beneficiaryId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )

        val response = httpClient.request(request).value(ListSerializer(ProjectExpense.serializer()))
        return@async response.sortedByDescending { it.beneficiaryTransaction.transactionDate }
    }

    suspend fun getProjectExpensesByProjectId(projectId: Long): List<ProjectExpense> = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/expenses"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(ListSerializer(ProjectExpense.serializer()))
        return@async response.sortedByDescending { it.beneficiaryTransaction.transactionDate }
    }

    suspend fun getIncomesByCustomerId(customerId: Long): List<ProjectIncome> = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/incomes/customers/$customerId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )

        val response = httpClient.request(request).value(ListSerializer(ProjectIncome.serializer()))
        return@async response.sortedByDescending { it.createdAt }
    }

    suspend fun getProjectIncomesByProjectId(projectId: Long): List<ProjectIncome> = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/incomes"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.GET,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get())
            )
        )
        val response = httpClient.request(request).value(ListSerializer(ProjectIncome.serializer()))
        return@async response.sortedByDescending { it.createdAt }
    }

    suspend fun addExpense(
        projectId: Long,
        beneficiaryId: Long?,
        termId: Long?,
        amount: AppDouble,
        termsGroupId: Long?,
        description: String,
        transactionDateIseoString: String
    ): ProjectExpense = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/expenses"
        val body = buildJsonObject {
            put("beneficiaryId", beneficiaryId)
            put("termId", termId)
            put("termsGroupId", termsGroupId)
            put("amount", amount)
            put("description", description.trim())
            put("transactionDate", transactionDateIseoString.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(ProjectExpense.serializer())
        return@async response
    }

    suspend fun addIncome(
        projectId: Long,
        customerId: Long,
        amount: AppDouble,
        description: String,
        transactionDateIseoString: String
    ): ProjectIncome = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/incomes"
        val body = buildJsonObject {
            put("customerId", customerId)
            put("amount", amount)
            put("description", description.trim())
            put("transactionDate", transactionDateIseoString.trim())
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(ProjectIncome.serializer())
        return@async response
    }

    suspend fun addUserAccess(
        projectId: Long,
        userId: Long,
        expensesAccess: Boolean,
        incomesAccess: Boolean
    ): Unit =
        logger.async {
            val url = "$apiBaseUrl/api/v1/projects/$projectId/access"
            val body = buildJsonObject {
                put("userId", userId)
                put("expensesAccess", expensesAccess)
                put("incomesAccess", incomesAccess)
            }
            val request = HttpRequest(
                url = url,
                method = HttpRequest.Method.POST,
                headers = listOf(
                    HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                    HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
                ),
                body = body
            )

            httpClient.request(request)
        }

    suspend fun modifyUserAccess(
        projectId: Long,
        userId: Long,
        expensesAccess: Boolean,
        incomesAccess: Boolean,
        version: Long
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/access"
        val body = buildJsonObject {
            put("userId", userId)
            put("expensesAccess", expensesAccess)
            put("incomesAccess", incomesAccess)
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        httpClient.request(request)
    }

    suspend fun removeUserAccess(projectId: Long, userId: Long) = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/access"
        val body = buildJsonObject {
            put("userId", userId)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.DELETE,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        httpClient.request(request)
    }

    suspend fun edit(
        projectId: Long,
        name: String,
        description: String,
        customerId: Long,
        version: Long
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId"
        val body = buildJsonObject {
            put("name", name.trim())
            put("description", description.trim())
            put("customerId", customerId)
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request)

        return@async response.value(Project.serializer())
    }

    suspend fun changeIncomeAmount(
        amount: AppDouble,
        amountPaid: AppDouble,
        projectIncomeId: Long,
        projectId: Long,
        transactionVersion: Long
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/incomes/$projectIncomeId/amount"
        val body = buildJsonObject {
            put("amount", amount)
            put("amountPaid", amountPaid)
            put("transactionVersion", transactionVersion)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request)

        return@async response.value(ProjectIncome.serializer())
    }

    suspend fun deleteProjectIncome(projectIncomeId: Long, projectId: Long): Unit = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/incomes/$projectIncomeId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.DELETE,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            )
        )

        httpClient.request(request)
    }

    suspend fun changeExpenseAmount(
        amount: AppDouble,
        amountPaid: AppDouble,
        projectExpenseId: Long,
        projectId: Long,
        transactionVersion: Long
    ) = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/expenses/$projectExpenseId/amount"
        val body = buildJsonObject {
            put("amount", amount)
            put("amountPaid", amountPaid)
            put("transactionVersion", transactionVersion)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request)

        return@async response.value(ProjectExpense.serializer())
    }

    suspend fun deleteProjectExpense(projectExpenseId: Long, projectId: Long): Unit = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/expenses/$projectExpenseId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.DELETE,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            )
        )

        httpClient.request(request)
    }

    suspend fun patchProjectExpense(
        projectExpenseId: Long,
        projectId: Long,
        termsGroupId: Long,
        termId: Long,
        version: Long,
    ): ProjectExpense = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId/expenses/$projectExpenseId"
        val body = buildJsonObject {
            put("termsGroupId", termsGroupId)
            put("termId", termId)
            put("version", version)
        }
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.PATCH,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            ),
            body = body
        )

        val response = httpClient.request(request).value(ProjectExpense.serializer())
        return@async response
    }

    suspend fun deleteProject(projectId: Long) = logger.async {
        val url = "$apiBaseUrl/api/v1/projects/$projectId"
        val request = HttpRequest(
            url = url,
            method = HttpRequest.Method.DELETE,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
                HttpHeader(type = HttpHeader.Type.CONTENT_TYPE, value = "application/json")
            )
        )

        httpClient.request(request)
    }

    suspend fun expensesForm(form: String, bytes: ByteArray): ExpensesFormResponse = logger.async {
        val url = "$apiBaseUrl/api/v1/ai/project/expenses/form"
        val request = HttpFormRequest(
            url = url,
            method = HttpRequest.Method.POST,
            headers = listOf(
                HttpHeader(type = HttpHeader.Type.AUTHORIZATION, value = UserSettings.userAccessToken.get()),
            ),
            body = listOf(
                FormElement.Input(name = "form", value = form),
                FormElement.File(name = "content", value = bytes, fileName = "recording.mp3", mimeType = "audio/aac")
            )
        )

        val raw = httpClient.request(request)
        val response = raw.value(ExpensesFormResponse.serializer())
        println(raw.body)
        return@async response
    }
}