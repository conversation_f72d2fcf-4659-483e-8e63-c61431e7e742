package com.ryinex.accountant.client.shared.domain.common.utils

import com.ryinex.accountant.client.shared.data.projects.models.ProjectExpense
import com.ryinex.kotlin.csv.CsvFile
import com.ryinex.kotlin.csv.CsvReadWrite
import com.ryinex.kotlin.csv.build
import core.common.extensions.dateFormat

internal fun List<ProjectExpense>.csv(name: String): CsvFile {
    return CsvReadWrite.build(this, true)
        .column("القيمة") { _, it -> it.beneficiaryTransaction.amount.formatted() }
        .column("الوصف") { _, it -> it.beneficiaryTransaction.description }
        .column("المشروع") { _, it -> it.project.name }
        .column("المجموعة") { _, it -> it.termsGroup?.name ?: "" }
        .column("البند") { _, it -> it.term?.name ?: "" }
        .column("التاريخ") { _, it -> it.createdAt.dateFormat() }
        .build(name)
}